State: 0, SolnCost: 38.00, # Moves: 38, # Nodes Gen: 2,156,821, Time: 6.35
State: 1, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,538,505, Time: 9.51
State: 2, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.17
State: 3, SolnCost: 61.00, # Moves: 61, # Nodes Gen: 3,996,821, Time: 11.47
State: 4, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,298,505, Time: 8.94
State: 5, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.59
State: 6, SolnCost: 66.00, # Moves: 66, # Nodes Gen: 4,364,181, Time: 12.79
State: 7, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,644,181, Time: 10.27
State: 8, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 8.87
State: 9, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,564,181, Time: 9.90
State: 10, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,644,181, Time: 10.15
State: 11, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,484,181, Time: 9.23
State: 12, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,898,505, Time: 8.04
State: 13, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.26
State: 14, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.67
State: 15, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,658,505, Time: 7.12
State: 16, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,564,181, Time: 9.95
State: 17, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.52
State: 18, SolnCost: 42.00, # Moves: 42, # Nodes Gen: 2,476,821, Time: 6.70
State: 19, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.59
State: 20, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.11
State: 21, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.04
State: 22, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.48
State: 23, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.36
State: 24, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,458,505, Time: 9.52
State: 25, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,858,505, Time: 10.28
State: 26, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,644,181, Time: 9.82
State: 27, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.65
State: 28, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 8.72
State: 29, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.08
State: 30, SolnCost: 65.00, # Moves: 65, # Nodes Gen: 4,284,181, Time: 11.53
State: 31, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.83
State: 32, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.62
State: 33, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,564,181, Time: 9.86
State: 34, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.15
State: 35, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,618,505, Time: 10.05
State: 36, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.68
State: 37, SolnCost: 40.00, # Moves: 40, # Nodes Gen: 2,316,821, Time: 6.39
State: 38, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.45
State: 39, SolnCost: 40.00, # Moves: 40, # Nodes Gen: 2,316,821, Time: 6.29
State: 40, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.53
State: 41, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,684,181, Time: 7.36
State: 42, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,738,505, Time: 7.36
State: 43, SolnCost: 38.00, # Moves: 38, # Nodes Gen: 2,178,505, Time: 5.96
State: 44, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.36
State: 45, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.48
State: 46, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,676,821, Time: 9.67
State: 47, SolnCost: 64.00, # Moves: 64, # Nodes Gen: 4,236,821, Time: 11.30
State: 48, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.60
State: 49, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.64
State: 50, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,644,181, Time: 9.77
State: 51, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,658,505, Time: 7.09
State: 52, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,676,821, Time: 10.00
State: 53, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.91
State: 54, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.31
State: 55, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,538,505, Time: 9.57
State: 56, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,658,505, Time: 7.31
State: 57, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.78
State: 58, SolnCost: 40.00, # Moves: 40, # Nodes Gen: 2,284,181, Time: 6.53
State: 59, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.27
State: 60, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,716,821, Time: 7.24
State: 61, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,724,181, Time: 9.86
State: 62, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.56
State: 63, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.40
State: 64, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.37
State: 65, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,778,505, Time: 10.21
State: 66, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.07
State: 67, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.94
State: 68, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.09
State: 69, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,676,821, Time: 9.71
State: 70, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.72
State: 71, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.03
State: 72, SolnCost: 64.00, # Moves: 64, # Nodes Gen: 4,236,821, Time: 11.28
State: 73, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,836,821, Time: 10.38
State: 74, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,676,821, Time: 10.17
State: 75, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.27
State: 76, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,458,505, Time: 9.44
State: 77, SolnCost: 40.00, # Moves: 40, # Nodes Gen: 2,284,181, Time: 6.24
State: 78, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,738,505, Time: 7.37
State: 79, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.78
State: 80, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,924,181, Time: 7.96
State: 81, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 9.31
State: 82, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.08
State: 83, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,858,505, Time: 10.14
State: 84, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 8.84
State: 85, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 8.97
State: 86, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 7.83
State: 87, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,818,505, Time: 7.66
State: 88, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.40
State: 89, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.83
State: 90, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,898,505, Time: 7.82
State: 91, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.54
State: 92, SolnCost: 63.00, # Moves: 63, # Nodes Gen: 4,178,505, Time: 11.31
State: 93, SolnCost: 41.00, # Moves: 41, # Nodes Gen: 2,418,505, Time: 6.64
State: 94, SolnCost: 42.00, # Moves: 42, # Nodes Gen: 2,444,181, Time: 6.76
State: 95, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,818,505, Time: 7.82
State: 96, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.75
State: 97, SolnCost: 63.00, # Moves: 63, # Nodes Gen: 4,156,821, Time: 10.97
State: 98, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.39
State: 99, SolnCost: 39.00, # Moves: 39, # Nodes Gen: 2,204,181, Time: 6.08
State: 100, SolnCost: 36.00, # Moves: 36, # Nodes Gen: 1,964,181, Time: 5.26
State: 101, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,538,505, Time: 9.30
State: 102, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,844,181, Time: 7.69
State: 103, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.07
State: 104, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.42
State: 105, SolnCost: 42.00, # Moves: 42, # Nodes Gen: 2,476,821, Time: 6.53
State: 106, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.48
State: 107, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.67
State: 108, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.96
State: 109, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.86
State: 110, SolnCost: 42.00, # Moves: 42, # Nodes Gen: 2,476,821, Time: 6.66
State: 111, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,084,181, Time: 8.31
State: 112, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 9.09
State: 113, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,836,821, Time: 10.13
State: 114, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,458,505, Time: 9.33
State: 115, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.62
State: 116, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.30
State: 117, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.58
State: 118, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.13
State: 119, SolnCost: 61.00, # Moves: 61, # Nodes Gen: 3,964,181, Time: 10.39
State: 120, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 7.85
State: 121, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.64
State: 122, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.93
State: 123, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.87
State: 124, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,978,505, Time: 7.90
State: 125, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,924,181, Time: 8.04
State: 126, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,578,505, Time: 6.88
State: 127, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,778,505, Time: 10.03
State: 128, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,524,181, Time: 6.83
State: 129, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.96
State: 130, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.07
State: 131, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,618,505, Time: 9.70
State: 132, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.84
State: 133, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,978,505, Time: 8.02
State: 134, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 9.71
State: 135, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.17
State: 136, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.84
State: 137, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.92
State: 138, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,578,505, Time: 7.06
State: 139, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,858,505, Time: 10.53
State: 140, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.91
State: 141, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.36
State: 142, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,298,505, Time: 8.87
State: 143, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 8.73
State: 144, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,716,821, Time: 7.32
State: 145, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.63
State: 146, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.52
State: 147, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.69
State: 148, SolnCost: 64.00, # Moves: 64, # Nodes Gen: 4,236,821, Time: 11.44
State: 149, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.63
State: 150, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.07
State: 151, SolnCost: 61.00, # Moves: 61, # Nodes Gen: 3,996,821, Time: 10.82
State: 152, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,084,181, Time: 8.47
State: 153, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.89
State: 154, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,458,505, Time: 9.15
State: 155, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,698,505, Time: 9.70
State: 156, SolnCost: 42.00, # Moves: 42, # Nodes Gen: 2,498,505, Time: 6.82
State: 157, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.58
State: 158, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.00
State: 159, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.70
State: 160, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,004,181, Time: 8.02
State: 161, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,778,505, Time: 10.11
State: 162, SolnCost: 41.00, # Moves: 41, # Nodes Gen: 2,418,505, Time: 6.33
State: 163, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.11
State: 164, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.51
State: 165, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.63
State: 166, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.50
State: 167, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.13
State: 168, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,084,181, Time: 8.19
State: 169, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,938,505, Time: 10.86
State: 170, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,538,505, Time: 9.46
State: 171, SolnCost: 42.00, # Moves: 42, # Nodes Gen: 2,498,505, Time: 6.64
State: 172, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.10
State: 173, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.28
State: 174, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.42
State: 175, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.19
State: 176, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.58
State: 177, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.32
State: 178, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.42
State: 179, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.58
State: 180, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.11
State: 181, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.80
State: 182, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.82
State: 183, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,564,181, Time: 9.70
State: 184, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.11
State: 185, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,556,821, Time: 6.90
State: 186, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.47
State: 187, SolnCost: 62.00, # Moves: 62, # Nodes Gen: 4,098,505, Time: 10.86
State: 188, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,618,505, Time: 9.76
State: 189, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,556,821, Time: 6.88
State: 190, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,644,181, Time: 9.93
State: 191, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,716,821, Time: 7.42
State: 192, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.22
State: 193, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,644,181, Time: 9.54
State: 194, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.61
State: 195, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.74
State: 196, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,978,505, Time: 8.08
State: 197, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.67
State: 198, SolnCost: 61.00, # Moves: 61, # Nodes Gen: 4,018,505, Time: 10.84
State: 199, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,836,821, Time: 10.78
State: 200, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,978,505, Time: 7.85
State: 201, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.04
State: 202, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.44
State: 203, SolnCost: 61.00, # Moves: 61, # Nodes Gen: 3,996,821, Time: 11.00
State: 204, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 9.93
State: 205, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.60
State: 206, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,764,181, Time: 7.32
State: 207, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.66
State: 208, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.11
State: 209, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,524,181, Time: 7.13
State: 210, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.26
State: 211, SolnCost: 69.00, # Moves: 69, # Nodes Gen: 4,658,505, Time: 12.94
State: 212, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.71
State: 213, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.42
State: 214, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.61
State: 215, SolnCost: 62.00, # Moves: 62, # Nodes Gen: 4,098,505, Time: 11.04
State: 216, SolnCost: 42.00, # Moves: 42, # Nodes Gen: 2,476,821, Time: 6.67
State: 217, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.56
State: 218, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,698,505, Time: 9.88
State: 219, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,778,505, Time: 10.41
State: 220, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.49
State: 221, SolnCost: 62.00, # Moves: 62, # Nodes Gen: 4,098,505, Time: 11.10
State: 222, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.77
State: 223, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.15
State: 224, SolnCost: 62.00, # Moves: 62, # Nodes Gen: 4,076,821, Time: 11.12
State: 225, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.33
State: 226, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.74
State: 227, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,004,181, Time: 8.09
State: 228, SolnCost: 61.00, # Moves: 61, # Nodes Gen: 3,996,821, Time: 10.67
State: 229, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.13
State: 230, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,084,181, Time: 8.52
State: 231, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,836,821, Time: 10.41
State: 232, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.76
State: 233, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,818,505, Time: 7.62
State: 234, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.48
State: 235, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.50
State: 236, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.10
State: 237, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.49
State: 238, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,778,505, Time: 9.95
State: 239, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,844,181, Time: 7.76
State: 240, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,404,181, Time: 9.16
State: 241, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.22
State: 242, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,716,821, Time: 7.43
State: 243, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.71
State: 244, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,556,821, Time: 6.99
State: 245, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.39
State: 246, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,636,821, Time: 7.15
State: 247, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.35
State: 248, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.02
State: 249, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.44
State: 250, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.11
State: 251, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,858,505, Time: 10.31
State: 252, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,884,181, Time: 10.38
State: 253, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.53
State: 254, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,724,181, Time: 10.01
State: 255, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,644,181, Time: 9.65
State: 256, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 8.90
State: 257, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.61
State: 258, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.02
State: 259, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.48
State: 260, SolnCost: 36.00, # Moves: 36, # Nodes Gen: 1,964,181, Time: 5.25
State: 261, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.43
State: 262, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.35
State: 263, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.51
State: 264, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.48
State: 265, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 7.78
State: 266, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.06
State: 267, SolnCost: 66.00, # Moves: 66, # Nodes Gen: 4,418,505, Time: 11.80
State: 268, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.52
State: 269, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.51
State: 270, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.42
State: 271, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.56
State: 272, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.51
State: 273, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,818,505, Time: 7.63
State: 274, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.56
State: 275, SolnCost: 69.00, # Moves: 69, # Nodes Gen: 4,636,821, Time: 12.37
State: 276, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.09
State: 277, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.27
State: 278, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,538,505, Time: 9.32
State: 279, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,644,181, Time: 9.88
State: 280, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.73
State: 281, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.53
State: 282, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 8.76
State: 283, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.51
State: 284, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.00
State: 285, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.24
State: 286, SolnCost: 39.00, # Moves: 39, # Nodes Gen: 2,236,821, Time: 6.17
State: 287, SolnCost: 39.00, # Moves: 39, # Nodes Gen: 2,236,821, Time: 6.09
State: 288, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.49
State: 289, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.01
State: 290, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.70
State: 291, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.26
State: 292, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 8.98
State: 293, SolnCost: 63.00, # Moves: 63, # Nodes Gen: 4,156,821, Time: 10.82
State: 294, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,804,181, Time: 10.28
State: 295, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,458,505, Time: 9.18
State: 296, SolnCost: 61.00, # Moves: 61, # Nodes Gen: 3,996,821, Time: 10.30
State: 297, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 7.95
State: 298, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 7.64
State: 299, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,564,181, Time: 9.56
State: 300, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.52
State: 301, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,298,505, Time: 8.94
State: 302, SolnCost: 62.00, # Moves: 62, # Nodes Gen: 4,098,505, Time: 10.85
State: 303, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.33
State: 304, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,658,505, Time: 7.04
State: 305, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.08
State: 306, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,724,181, Time: 9.83
State: 307, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,084,181, Time: 8.04
State: 308, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.07
State: 309, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,924,181, Time: 7.86
State: 310, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.37
State: 311, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.08
State: 312, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.76
State: 313, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.90
State: 314, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.39
State: 315, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.99
State: 316, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,738,505, Time: 7.40
State: 317, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.24
State: 318, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.68
State: 319, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.01
State: 320, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,978,505, Time: 7.57
State: 321, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,818,505, Time: 7.53
State: 322, SolnCost: 41.00, # Moves: 41, # Nodes Gen: 2,418,505, Time: 6.47
State: 323, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 8.94
State: 324, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,938,505, Time: 10.58
State: 325, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,058,505, Time: 8.12
State: 326, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.53
State: 327, SolnCost: 66.00, # Moves: 66, # Nodes Gen: 4,364,181, Time: 11.82
State: 328, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,636,821, Time: 7.02
State: 329, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.05
State: 330, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.51
State: 331, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,404,181, Time: 8.88
State: 332, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,738,505, Time: 7.41
State: 333, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,298,505, Time: 8.84
State: 334, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.36
State: 335, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.63
State: 336, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,556,821, Time: 6.86
State: 337, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,564,181, Time: 9.44
State: 338, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,764,181, Time: 7.60
State: 339, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.09
State: 340, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,324,181, Time: 9.19
State: 341, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.05
State: 342, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.01
State: 343, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.51
State: 344, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.35
State: 345, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.59
State: 346, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.71
State: 347, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,298,505, Time: 8.94
State: 348, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,564,181, Time: 9.76
State: 349, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.97
State: 350, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,556,821, Time: 6.91
State: 351, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.26
State: 352, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,298,505, Time: 8.97
State: 353, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.66
State: 354, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,538,505, Time: 9.14
State: 355, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 9.93
State: 356, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 7.85
State: 357, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.24
State: 358, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,778,505, Time: 10.03
State: 359, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.33
State: 360, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,676,821, Time: 9.82
State: 361, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.02
State: 362, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.17
State: 363, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,538,505, Time: 9.76
State: 364, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,716,821, Time: 7.26
State: 365, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,636,821, Time: 6.86
State: 366, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,836,821, Time: 10.22
State: 367, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.78
State: 368, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 8.13
State: 369, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.76
State: 370, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,058,505, Time: 8.02
State: 371, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.06
State: 372, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 9.96
State: 373, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.51
State: 374, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,756,821, Time: 10.31
State: 375, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.79
State: 376, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.43
State: 377, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,818,505, Time: 7.40
State: 378, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,796,821, Time: 7.59
State: 379, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.71
State: 380, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.10
State: 381, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.88
State: 382, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.04
State: 383, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,004,181, Time: 8.03
State: 384, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.21
State: 385, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.05
State: 386, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,818,505, Time: 7.43
State: 387, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.15
State: 388, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,484,181, Time: 9.39
State: 389, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,684,181, Time: 7.14
State: 390, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.44
State: 391, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,084,181, Time: 8.37
State: 392, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,684,181, Time: 7.37
State: 393, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 9.18
State: 394, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,778,505, Time: 10.03
State: 395, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,636,821, Time: 6.96
State: 396, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.80
State: 397, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,684,181, Time: 7.29
State: 398, SolnCost: 40.00, # Moves: 40, # Nodes Gen: 2,316,821, Time: 6.36
State: 399, SolnCost: 43.00, # Moves: 43, # Nodes Gen: 2,524,181, Time: 6.87
State: 400, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.86
State: 401, SolnCost: 58.00, # Moves: 58, # Nodes Gen: 3,724,181, Time: 10.05
State: 402, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,636,821, Time: 7.10
State: 403, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.76
State: 404, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.52
State: 405, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 9.02
State: 406, SolnCost: 64.00, # Moves: 64, # Nodes Gen: 4,236,821, Time: 11.39
State: 407, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,716,821, Time: 7.34
State: 408, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 9.11
State: 409, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.01
State: 410, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.99
State: 411, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.18
State: 412, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,898,505, Time: 7.84
State: 413, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 8.98
State: 414, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.94
State: 415, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.72
State: 416, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.19
State: 417, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,858,505, Time: 10.57
State: 418, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.45
State: 419, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,458,505, Time: 9.18
State: 420, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.13
State: 421, SolnCost: 62.00, # Moves: 62, # Nodes Gen: 4,044,181, Time: 10.85
State: 422, SolnCost: 62.00, # Moves: 62, # Nodes Gen: 4,076,821, Time: 10.84
State: 423, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,858,505, Time: 10.07
State: 424, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.54
State: 425, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.83
State: 426, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.67
State: 427, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,618,505, Time: 9.73
State: 428, SolnCost: 41.00, # Moves: 41, # Nodes Gen: 2,418,505, Time: 6.50
State: 429, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.57
State: 430, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 9.06
State: 431, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.64
State: 432, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,836,821, Time: 10.01
State: 433, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,818,505, Time: 7.83
State: 434, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,858,505, Time: 10.37
State: 435, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.26
State: 436, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.53
State: 437, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.70
State: 438, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.77
State: 439, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,378,505, Time: 8.83
State: 440, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.77
State: 441, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.48
State: 442, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,836,821, Time: 10.01
State: 443, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.53
State: 444, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.62
State: 445, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 7.77
State: 446, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,604,181, Time: 7.07
State: 447, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,858,505, Time: 10.71
State: 448, SolnCost: 40.00, # Moves: 40, # Nodes Gen: 2,338,505, Time: 6.32
State: 449, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,004,181, Time: 7.94
State: 450, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,244,181, Time: 8.74
State: 451, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,458,505, Time: 9.02
State: 452, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.05
State: 453, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.70
State: 454, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,676,821, Time: 9.97
State: 455, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,684,181, Time: 7.19
State: 456, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,636,821, Time: 7.09
State: 457, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,458,505, Time: 9.18
State: 458, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,484,181, Time: 9.23
State: 459, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,036,821, Time: 8.29
State: 460, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 8.03
State: 461, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.20
State: 462, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,484,181, Time: 9.36
State: 463, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,564,181, Time: 9.49
State: 464, SolnCost: 61.00, # Moves: 61, # Nodes Gen: 4,018,505, Time: 10.48
State: 465, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 7.90
State: 466, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 9.06
State: 467, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,844,181, Time: 7.92
State: 468, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.52
State: 469, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,804,181, Time: 10.32
State: 470, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,804,181, Time: 10.42
State: 471, SolnCost: 47.00, # Moves: 47, # Nodes Gen: 2,876,821, Time: 7.66
State: 472, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.43
State: 473, SolnCost: 60.00, # Moves: 60, # Nodes Gen: 3,916,821, Time: 10.84
State: 474, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,058,505, Time: 8.42
State: 475, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,836,821, Time: 10.52
State: 476, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.57
State: 477, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,658,505, Time: 7.20
State: 478, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.63
State: 479, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.81
State: 480, SolnCost: 44.00, # Moves: 44, # Nodes Gen: 2,658,505, Time: 7.34
State: 481, SolnCost: 57.00, # Moves: 57, # Nodes Gen: 3,676,821, Time: 9.82
State: 482, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,956,821, Time: 8.21
State: 483, SolnCost: 53.00, # Moves: 53, # Nodes Gen: 3,356,821, Time: 9.08
State: 484, SolnCost: 59.00, # Moves: 59, # Nodes Gen: 3,804,181, Time: 10.41
State: 485, SolnCost: 46.00, # Moves: 46, # Nodes Gen: 2,764,181, Time: 7.58
State: 486, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,058,505, Time: 8.39
State: 487, SolnCost: 54.00, # Moves: 54, # Nodes Gen: 3,436,821, Time: 9.32
State: 488, SolnCost: 48.00, # Moves: 48, # Nodes Gen: 2,978,505, Time: 8.00
State: 489, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,218,505, Time: 8.66
State: 490, SolnCost: 55.00, # Moves: 55, # Nodes Gen: 3,516,821, Time: 9.15
State: 491, SolnCost: 49.00, # Moves: 49, # Nodes Gen: 3,004,181, Time: 7.93
State: 492, SolnCost: 52.00, # Moves: 52, # Nodes Gen: 3,276,821, Time: 8.71
State: 493, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,164,181, Time: 8.43
State: 494, SolnCost: 37.00, # Moves: 37, # Nodes Gen: 2,098,505, Time: 5.58
State: 495, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,138,505, Time: 8.54
State: 496, SolnCost: 51.00, # Moves: 51, # Nodes Gen: 3,196,821, Time: 8.67
State: 497, SolnCost: 50.00, # Moves: 50, # Nodes Gen: 3,116,821, Time: 8.61
State: 498, SolnCost: 56.00, # Moves: 56, # Nodes Gen: 3,596,821, Time: 9.67
State: 499, SolnCost: 45.00, # Moves: 45, # Nodes Gen: 2,716,821, Time: 7.43
