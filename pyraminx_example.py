#!/usr/bin/env python3
"""
Example usage of the Pyraminx implementation for DeepCubeA

This script demonstrates how to:
1. Create and manipulate Pyraminx states
2. Apply moves and scramble the puzzle
3. Use the environment for training data generation
4. Integrate with the DeepCubeA framework
"""

import numpy as np
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from environments.pyraminx import Pyraminx, PyraminxState


def demonstrate_basic_usage():
    """Demonstrate basic Pyraminx usage"""
    print("=== Basic Pyraminx Usage ===")
    
    # Create the environment
    env = Pyraminx()
    print(f"Created Pyraminx environment with {env.get_num_moves()} moves")
    print(f"Available moves: {env.moves}")
    
    # Create a solved state
    solved_state = env.generate_goal_states(1)[0]
    print(f"\nSolved state colors (first 12): {solved_state.colors[:12]}...")
    
    # Check if it's solved
    is_solved = env.is_solved([solved_state])
    print(f"Is solved: {is_solved[0]}")
    
    return env, solved_state


def demonstrate_moves():
    """Demonstrate applying moves to the Pyraminx"""
    print("\n=== Applying Moves ===")
    
    env = Pyraminx()
    state = env.generate_goal_states(1)[0]
    
    print("Starting with solved state")
    print("Applying sequence of moves: F1, R-1, B1, L-1")
    
    # Apply a sequence of moves
    move_sequence = [0, 3, 4, 7]  # F1, R-1, B1, L-1
    current_state = state
    
    for i, move_idx in enumerate(move_sequence):
        move_name = env.moves[move_idx]
        next_states, costs = env.next_state([current_state], move_idx)
        current_state = next_states[0]
        
        is_solved = env.is_solved([current_state])
        print(f"  Step {i+1}: Applied {move_name}, solved: {is_solved[0]}")
    
    print(f"Final state colors (first 12): {current_state.colors[:12]}...")
    return current_state


def demonstrate_scrambling():
    """Demonstrate state scrambling"""
    print("\n=== State Scrambling ===")
    
    env = Pyraminx()
    
    # Generate scrambled states with different depths
    print("Generating scrambled states...")
    
    # Light scrambles (1-5 moves)
    light_states, light_depths = env.generate_states(5, (1, 5))
    print(f"Light scrambles: {light_depths}")
    
    # Medium scrambles (5-10 moves)  
    medium_states, medium_depths = env.generate_states(5, (5, 10))
    print(f"Medium scrambles: {medium_depths}")
    
    # Heavy scrambles (10-20 moves)
    heavy_states, heavy_depths = env.generate_states(5, (10, 20))
    print(f"Heavy scrambles: {heavy_depths}")
    
    # Check how many are solved (should be very few or none)
    all_states = light_states + medium_states + heavy_states
    solved_check = env.is_solved(all_states)
    num_solved = np.sum(solved_check)
    
    print(f"Out of {len(all_states)} scrambled states, {num_solved} are solved")
    
    return heavy_states[0]  # Return a heavily scrambled state


def demonstrate_neural_network_input():
    """Demonstrate neural network input preparation"""
    print("\n=== Neural Network Input ===")
    
    env = Pyraminx()
    
    # Generate some states
    states, _ = env.generate_states(3, (1, 10))
    
    # Convert to neural network input
    nnet_input = env.state_to_nnet_input(states)
    
    print(f"Number of states: {len(states)}")
    print(f"Neural network input shape: {nnet_input[0].shape}")
    print(f"Input data type: {nnet_input[0].dtype}")
    print(f"Input range: [{nnet_input[0].min():.3f}, {nnet_input[0].max():.3f}]")
    
    # Show the input for first state
    print(f"First state input (first 12 values): {nnet_input[0][0][:12]}")
    
    return nnet_input


def demonstrate_state_expansion():
    """Demonstrate state expansion (getting all possible next states)"""
    print("\n=== State Expansion ===")
    
    env = Pyraminx()
    
    # Start with a scrambled state
    states, _ = env.generate_states(2, (3, 5))
    
    # Expand states (get all possible next states)
    expanded_states, transition_costs = env.expand(states)
    
    print(f"Expanded {len(states)} states")
    print(f"Each state has {len(expanded_states[0])} possible next states")
    print(f"All transition costs are: {transition_costs[0]}")
    
    # Check some properties
    for i, state_children in enumerate(expanded_states):
        print(f"State {i}: {len(state_children)} children")
        
        # Check if any children are solved
        solved_children = env.is_solved(state_children)
        num_solved_children = np.sum(solved_children)
        if num_solved_children > 0:
            print(f"  -> {num_solved_children} children are solved!")
    
    return expanded_states


def demonstrate_training_data_generation():
    """Demonstrate generating training data"""
    print("\n=== Training Data Generation ===")
    
    env = Pyraminx()
    
    # Generate training data with various scramble depths
    print("Generating training data...")
    
    # This is similar to what the training process would do
    training_states, scramble_depths = env.generate_states(1000, (1, 15))
    
    print(f"Generated {len(training_states)} training states")
    print(f"Scramble depth distribution:")
    
    # Show distribution of scramble depths
    unique_depths, counts = np.unique(scramble_depths, return_counts=True)
    for depth, count in zip(unique_depths, counts):
        print(f"  {depth} moves: {count} states")
    
    # Convert to neural network format
    nnet_inputs = env.state_to_nnet_input(training_states)
    
    print(f"Training data shape: {nnet_inputs[0].shape}")
    print(f"Memory usage: ~{nnet_inputs[0].nbytes / 1024 / 1024:.1f} MB")
    
    return training_states, scramble_depths


def demonstrate_model_creation():
    """Demonstrate neural network model creation"""
    print("\n=== Neural Network Model ===")
    
    env = Pyraminx()
    
    # Create the neural network model
    model = env.get_nnet_model()
    
    print(f"Created model: {type(model).__name__}")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # Test forward pass
    import torch
    
    # Create dummy input (batch of 5 states)
    dummy_input = torch.randint(0, 4, (5, env.total_stickers))
    
    with torch.no_grad():
        output = model(dummy_input)
    
    print(f"Model input shape: {dummy_input.shape}")
    print(f"Model output shape: {output.shape}")
    print(f"Sample outputs: {output.flatten()[:5].tolist()}")
    
    return model


def main():
    """Run all demonstrations"""
    print("Pyraminx DeepCubeA Implementation Example")
    print("=" * 50)
    
    try:
        # Run all demonstrations
        env, solved_state = demonstrate_basic_usage()
        scrambled_state = demonstrate_moves()
        heavy_scrambled = demonstrate_scrambling()
        nnet_input = demonstrate_neural_network_input()
        expanded = demonstrate_state_expansion()
        training_data, depths = demonstrate_training_data_generation()
        model = demonstrate_model_creation()
        
        print("\n" + "=" * 50)
        print("🎉 All demonstrations completed successfully!")
        print("\nNext steps:")
        print("1. Run 'python test_pyraminx.py' to verify implementation")
        print("2. Run './train_pyraminx.sh' to train the model")
        print("3. Use the trained model to solve Pyraminx puzzles")
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
