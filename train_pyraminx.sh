#!/bin/bash

### Pyraminx Training and Solving Script for DeepCubeA

echo "Starting Pyraminx DeepCubeA Training..."

# Create necessary directories
mkdir -p saved_models/pyraminx/current
mkdir -p saved_models/pyraminx/target
mkdir -p results/pyraminx
mkdir -p data/pyraminx/test

echo "Directories created."

###### Train cost-to-go function for Pyraminx
echo "Training cost-to-go function..."

# First training phase - adjusted for 16 moves
python ctg_approx/avi.py \
    --env pyraminx \
    --states_per_update 25000000 \
    --batch_size 6000 \
    --nnet_name pyraminx \
    --max_itrs 600000 \
    --loss_thresh 0.1 \
    --back_max 25 \
    --num_update_procs 20

echo "First training phase completed. Updating target network..."

# Manually update target network
cp -r saved_models/pyraminx/current/* saved_models/pyraminx/target/

echo "Target network updated. Starting second training phase..."

# Second training phase with more iterations
python ctg_approx/avi.py \
    --env pyraminx \
    --states_per_update 25000000 \
    --batch_size 6000 \
    --nnet_name pyraminx \
    --max_itrs 1000000 \
    --loss_thresh 0.1 \
    --back_max 25 \
    --num_update_procs 20

echo "Training completed!"

###### Generate test data
echo "Generating test data..."

python -c "
import pickle
import numpy as np
from environments.pyraminx import Pyraminx

env = Pyraminx()
states, scramble_nums = env.generate_states(1000, (1, 25))

# Save test data
data = {
    'states': states,
    'scramble_nums': scramble_nums
}

with open('data/pyraminx/test/data_0.pkl', 'wb') as f:
    pickle.dump(data, f)

print('Test data generated: 1000 states with scrambles 1-25')
print(f'Available moves: {env.moves}')
print(f'Total moves: {env.get_num_moves()}')
"

echo "Test data generated."

###### Solve with A* search
echo "Solving Pyraminx with A* search..."

python search_methods/astar.py \
    --states data/pyraminx/test/data_0.pkl \
    --model saved_models/pyraminx/current/ \
    --env pyraminx \
    --weight 0.7 \
    --batch_size 8000 \
    --results_dir results/pyraminx/ \
    --nnet_batch_size 8000 \
    --verbose

echo "A* search completed."

###### Compare solutions to optimal (if available)
echo "Analyzing results..."

python scripts/compare_solutions.py \
    --soln1 data/pyraminx/test/data_0.pkl \
    --soln2 results/pyraminx/results.pkl

echo "Results analysis completed."

###### Display summary
echo "=== Pyraminx DeepCubeA Training Summary ==="
echo "Training completed successfully!"
echo "Model saved in: saved_models/pyraminx/current/"
echo "Test results in: results/pyraminx/"
echo "Check the results for solving performance."

# Optional: Run additional tests with different scramble depths
echo "Running additional tests with deeper scrambles..."

python -c "
import pickle
import numpy as np
from environments.pyraminx import Pyraminx

env = Pyraminx()

# Test with deeper scrambles (15-30 moves)
states_deep, scramble_nums_deep = env.generate_states(500, (15, 30))

data_deep = {
    'states': states_deep,
    'scramble_nums': scramble_nums_deep
}

with open('data/pyraminx/test/data_deep.pkl', 'wb') as f:
    pickle.dump(data_deep, f)

print('Deep scramble test data generated: 500 states with scrambles 15-30')
"

# Solve deep scrambles
python search_methods/astar.py \
    --states data/pyraminx/test/data_deep.pkl \
    --model saved_models/pyraminx/current/ \
    --env pyraminx \
    --weight 0.7 \
    --batch_size 4000 \
    --results_dir results/pyraminx/ \
    --nnet_batch_size 4000 \
    --results_file results_deep.pkl

echo "Deep scramble testing completed."
echo "All Pyraminx training and testing finished!"
