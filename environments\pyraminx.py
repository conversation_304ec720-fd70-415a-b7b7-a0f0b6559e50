from typing import List, Dict, Tu<PERSON>, Union
import numpy as np
from torch import nn
from random import randrange

from utils.pytorch_models import ResnetModel
from .environment_abstract import Environment, State


class PyraminxState(State):
    __slots__ = ['colors', 'hash']

    def __init__(self, colors: np.ndarray):
        self.colors: np.ndarray = colors
        self.hash = None

    def __hash__(self):
        if self.hash is None:
            self.hash = hash(self.colors.tostring())

        return self.hash

    def __eq__(self, other):
        return np.array_equal(self.colors, other.colors)


class Pyraminx(Environment):
    """
    Pyraminx puzzle implementation for DeepCubeA

    The Pyraminx is a tetrahedral puzzle with 4 faces, each face is a triangle.
    Each face has 9 stickers arranged in a triangular pattern:

        0
       1 2
      3 4 5
     6 7 8 9

    Total: 4 faces × 9 stickers = 36 stickers

    Moves:
    - Tip rotations: U, R, L, B (4 tips × 2 directions = 8 moves)
    - Edge rotations: u, r, l, b (4 edges × 2 directions = 8 moves)
    Total moves: 16 moves

    Move notation:
    - U/U': Top tip clockwise/counterclockwise
    - R/R': Right tip clockwise/counterclockwise
    - L/L': Left tip clockwise/counterclockwise
    - B/B': Back tip clockwise/counterclockwise
    - u/u': Top edge clockwise/counterclockwise
    - r/r': Right edge clockwise/counterclockwise
    - l/l': Left edge clockwise/counterclockwise
    - b/b': Back edge clockwise/counterclockwise
    """

    # Tip moves (uppercase) and edge moves (lowercase)
    moves: List[str] = (
        ["%s%s" % (f, n) for f in ['U', 'R', 'L', 'B'] for n in ['', "'"]] +  # Tip moves
        ["%s%s" % (f, n) for f in ['u', 'r', 'l', 'b'] for n in ['', "'"]]    # Edge moves
    )
    moves_rev: List[str] = (
        ["%s%s" % (f, n) for f in ['U', 'R', 'L', 'B'] for n in ["'", '']] +  # Tip moves reversed
        ["%s%s" % (f, n) for f in ['u', 'r', 'l', 'b'] for n in ["'", '']]    # Edge moves reversed
    )

    def __init__(self):
        super().__init__()
        self.dtype = np.uint8
        self.num_stickers_per_face = 9
        self.num_faces = 4
        self.total_stickers = self.num_faces * self.num_stickers_per_face

        # solved state: each face has its own color (0, 1, 2, 3)
        self.goal_colors: np.ndarray = np.array([
            # Face 0 (Top/Front): all 0s
            [0, 0, 0, 0, 0, 0, 0, 0, 0],
            # Face 1 (Right): all 1s
            [1, 1, 1, 1, 1, 1, 1, 1, 1],
            # Face 2 (Left): all 2s
            [2, 2, 2, 2, 2, 2, 2, 2, 2],
            # Face 3 (Back): all 3s
            [3, 3, 3, 3, 3, 3, 3, 3, 3]
        ], dtype=self.dtype).flatten()

        # Define face adjacencies and sticker mappings
        self._setup_face_structure()

        # Define rotation mappings for each move
        self.rotate_idxs_new: Dict[str, np.ndarray]
        self.rotate_idxs_old: Dict[str, np.ndarray]
        self._compute_rotation_idxs()

    def next_state(self, states: List[PyraminxState], action: int) -> Tuple[List[PyraminxState], List[float]]:
        states_np = np.stack([x.colors for x in states], axis=0)
        states_next_np, transition_costs = self._move_np(states_np, action)

        states_next: List[PyraminxState] = [PyraminxState(x) for x in list(states_next_np)]

        return states_next, transition_costs

    def prev_state(self, states: List[PyraminxState], action: int) -> List[PyraminxState]:
        move: str = self.moves[action]
        move_rev_idx: int = np.where(np.array(self.moves_rev) == np.array(move))[0][0]

        return self.next_state(states, move_rev_idx)[0]

    def generate_goal_states(self, num_states: int, np_format: bool = False) -> Union[List[PyraminxState], np.ndarray]:
        if np_format:
            goal_np: np.ndarray = np.expand_dims(self.goal_colors.copy(), 0)
            solved_states: np.ndarray = np.repeat(goal_np, num_states, axis=0)
        else:
            solved_states: List[PyraminxState] = [PyraminxState(self.goal_colors.copy()) for _ in range(num_states)]

        return solved_states

    def is_solved(self, states: List[PyraminxState]) -> np.ndarray:
        states_np = np.stack([state.colors for state in states], axis=0)
        is_equal = np.equal(states_np, np.expand_dims(self.goal_colors, 0))

        return np.all(is_equal, axis=1)

    def state_to_nnet_input(self, states: List[PyraminxState]) -> List[np.ndarray]:
        states_np = np.stack([state.colors for state in states], axis=0)

        # Normalize colors to [0, 1] range
        representation_np: np.ndarray = states_np / (self.num_faces - 1)
        representation_np: np.ndarray = representation_np.astype(np.float32)

        representation: List[np.ndarray] = [representation_np]

        return representation

    def get_num_moves(self) -> int:
        return len(self.moves)

    def get_nnet_model(self) -> nn.Module:
        state_dim: int = self.total_stickers
        # Neural network architecture for Pyraminx
        # Input: 36 stickers, 4 colors, hidden layers: 2000 -> 500, 3 ResNet blocks
        # Increased complexity due to 16 moves vs 8 in original
        nnet = ResnetModel(state_dim, 4, 2000, 500, 3, 1, True)

        return nnet

    def _setup_face_structure(self):
        """
        Setup the face structure and adjacencies for the Pyraminx.

        Pyraminx face arrangement (tetrahedral):
        - Face 0: Top/Front (U moves)
        - Face 1: Right (R moves)
        - Face 2: Left (L moves)
        - Face 3: Back (B moves)

        Each face shares edges with the other 3 faces.
        """

        # Define which faces are adjacent to each face
        # Each face is adjacent to all other faces in a tetrahedron
        self.face_adjacencies = {
            0: [1, 2, 3],  # Top adjacent to Right, Left, Back
            1: [0, 2, 3],  # Right adjacent to Top, Left, Back
            2: [0, 1, 3],  # Left adjacent to Top, Right, Back
            3: [0, 1, 2]   # Back adjacent to Top, Right, Left
        }

        # Define sticker positions for tips and edges
        # Tips are the corner stickers (position 0 on each face)
        self.tip_positions = {
            0: 0,  # Top tip
            1: 0,  # Right tip
            2: 0,  # Left tip
            3: 0   # Back tip
        }

        # Edge positions are the stickers that form the edges between faces
        # For each face, define which stickers are on edges with adjacent faces
        self.edge_positions = {
            # Face 0 (Top) edges with other faces
            0: {
                1: [1, 3],    # Edge with Right face
                2: [2, 4],    # Edge with Left face
                3: [5, 6]     # Edge with Back face
            },
            # Face 1 (Right) edges with other faces
            1: {
                0: [1, 3],    # Edge with Top face
                2: [2, 4],    # Edge with Left face
                3: [5, 6]     # Edge with Back face
            },
            # Face 2 (Left) edges with other faces
            2: {
                0: [1, 3],    # Edge with Top face
                1: [2, 4],    # Edge with Right face
                3: [5, 6]     # Edge with Back face
            },
            # Face 3 (Back) edges with other faces
            3: {
                0: [1, 3],    # Edge with Top face
                1: [2, 4],    # Edge with Right face
                2: [5, 6]     # Edge with Left face
            }
        }

    def generate_states(self, num_states: int, backwards_range: Tuple[int, int]) -> Tuple[List[PyraminxState], List[int]]:
        assert (num_states > 0)
        assert (backwards_range[0] >= 0)
        assert self.fixed_actions, "Environments without fixed actions must implement their own method"

        # Initialize
        scrambs: List[int] = list(range(backwards_range[0], backwards_range[1] + 1))
        num_env_moves: int = self.get_num_moves()

        # Get goal states
        states_np: np.ndarray = self.generate_goal_states(num_states, np_format=True)

        # Scrambles
        scramble_nums: np.array = np.random.choice(scrambs, num_states)
        num_back_moves: np.array = np.zeros(num_states)

        # Go backward from goal state
        moves_lt = num_back_moves < scramble_nums
        while np.any(moves_lt):
            idxs: np.ndarray = np.where(moves_lt)[0]
            subset_size: int = int(max(len(idxs) / num_env_moves, 1))
            idxs: np.ndarray = np.random.choice(idxs, subset_size)

            move: int = randrange(num_env_moves)
            states_np[idxs], _ = self._move_np(states_np[idxs], move)

            num_back_moves[idxs] = num_back_moves[idxs] + 1
            moves_lt[idxs] = num_back_moves[idxs] < scramble_nums[idxs]

        states: List[PyraminxState] = [PyraminxState(x) for x in list(states_np)]

        return states, scramble_nums.tolist()

    def expand(self, states: List[State]) -> Tuple[List[List[State]], List[np.ndarray]]:
        assert self.fixed_actions, "Environments without fixed actions must implement their own method"

        # initialize
        num_states: int = len(states)
        num_env_moves: int = self.get_num_moves()

        states_exp: List[List[State]] = [[] for _ in range(len(states))]

        tc: np.ndarray = np.empty([num_states, num_env_moves])

        # numpy states
        states_np: np.ndarray = np.stack([state.colors for state in states])

        # for each move, get next states, transition costs, and if solved
        for move_idx in range(num_env_moves):
            # next state
            states_next_np: np.ndarray
            tc_move: List[float]
            states_next_np, tc_move = self._move_np(states_np, move_idx)

            # transition cost
            tc[:, move_idx] = np.array(tc_move)

            for idx in range(len(states)):
                states_exp[idx].append(PyraminxState(states_next_np[idx]))

        # make lists
        tc_l: List[np.ndarray] = [tc[i] for i in range(num_states)]

        return states_exp, tc_l

    def _move_np(self, states_np: np.ndarray, action: int):
        action_str: str = self.moves[action]

        states_next_np: np.ndarray = states_np.copy()
        states_next_np[:, self.rotate_idxs_new[action_str]] = states_np[:, self.rotate_idxs_old[action_str]]

        transition_costs: List[float] = [1.0 for _ in range(states_np.shape[0])]

        return states_next_np, transition_costs

    def _compute_rotation_idxs(self):
        """
        Compute rotation indices for Pyraminx moves.

        Pyraminx has 16 moves:
        - 8 tip moves: U, U', R, R', L, L', B, B'
        - 8 edge moves: u, u', r, r', l, l', b, b'

        Face layout (flattened):
        Face 0 (Top):    [0-8]
        Face 1 (Right):  [9-17]
        Face 2 (Left):   [18-26]
        Face 3 (Back):   [27-35]

        Sticker arrangement per face:
            0
           1 2
          3 4 5
         6 7 8 9
        """

        self.rotate_idxs_new: Dict[str, np.ndarray] = dict()
        self.rotate_idxs_old: Dict[str, np.ndarray] = dict()

        # Face mapping
        face_map = {'U': 0, 'R': 1, 'L': 2, 'B': 3,
                   'u': 0, 'r': 1, 'l': 2, 'b': 3}

        for move in self.moves:
            face_char = move[0]
            is_prime = "'" in move
            is_tip_move = face_char.isupper()

            face_num = face_map[face_char]

            self.rotate_idxs_new[move] = np.array([], dtype=int)
            self.rotate_idxs_old[move] = np.array([], dtype=int)

            if is_tip_move:
                # Tip moves only affect the tip (corner) of the face
                self._add_tip_rotation(move, face_num, is_prime)
            else:
                # Edge moves affect the second layer
                self._add_edge_rotation(move, face_num, is_prime)

    def _add_tip_rotation(self, move: str, face_num: int, is_prime: bool):
        """Add tip rotation indices for a move"""

        # Tip rotations only affect position 0 (the tip) of the face
        # and potentially some adjacent stickers

        face_offset = face_num * self.num_stickers_per_face

        # For tip moves, we rotate the tip and its immediate neighbors
        if not is_prime:  # Clockwise
            # Rotate positions 0, 1, 2 clockwise
            old_positions = [0, 1, 2]
            new_positions = [0, 2, 1]  # Tip stays, 1->2, 2->1
        else:  # Counterclockwise
            old_positions = [0, 1, 2]
            new_positions = [0, 1, 2]  # For prime moves, reverse the rotation

        for old_pos, new_pos in zip(old_positions, new_positions):
            old_idx = face_offset + old_pos
            new_idx = face_offset + new_pos
            self.rotate_idxs_old[move] = np.append(self.rotate_idxs_old[move], old_idx)
            self.rotate_idxs_new[move] = np.append(self.rotate_idxs_new[move], new_idx)

    def _add_edge_rotation(self, move: str, face_num: int, is_prime: bool):
        """Add edge rotation indices for a move"""

        face_offset = face_num * self.num_stickers_per_face

        # Edge moves affect the second layer (positions 3, 4, 5)
        # and also cycle stickers between adjacent faces

        if not is_prime:  # Clockwise
            # Rotate the second layer of the face
            old_positions = [3, 4, 5]
            new_positions = [5, 3, 4]  # Cycle clockwise
        else:  # Counterclockwise
            old_positions = [3, 4, 5]
            new_positions = [4, 5, 3]  # Cycle counterclockwise

        # Add face rotation
        for old_pos, new_pos in zip(old_positions, new_positions):
            old_idx = face_offset + old_pos
            new_idx = face_offset + new_pos
            self.rotate_idxs_old[move] = np.append(self.rotate_idxs_old[move], old_idx)
            self.rotate_idxs_new[move] = np.append(self.rotate_idxs_new[move], new_idx)

        # Add edge cycling between adjacent faces
        self._add_edge_cycling(move, face_num, is_prime)

    def _add_edge_cycling(self, move: str, face_num: int, is_prime: bool):
        """Add edge cycling between adjacent faces for edge moves"""

        # Define which stickers cycle between faces for each edge move
        edge_cycles = {
            0: {  # Top face edge move cycles
                'positions': [1, 2],  # Positions that cycle
                'faces': [1, 3, 2],   # Right -> Back -> Left -> Right
                'face_positions': {
                    1: [1, 3],  # Right face positions
                    2: [2, 4],  # Left face positions
                    3: [1, 3]   # Back face positions
                }
            },
            1: {  # Right face edge move cycles
                'positions': [1, 2],
                'faces': [0, 3, 2],   # Top -> Back -> Left -> Top
                'face_positions': {
                    0: [1, 3],  # Top face positions
                    2: [2, 4],  # Left face positions
                    3: [2, 4]   # Back face positions
                }
            },
            2: {  # Left face edge move cycles
                'positions': [1, 2],
                'faces': [0, 1, 3],   # Top -> Right -> Back -> Top
                'face_positions': {
                    0: [2, 4],  # Top face positions
                    1: [2, 4],  # Right face positions
                    3: [1, 3]   # Back face positions
                }
            },
            3: {  # Back face edge move cycles
                'positions': [1, 2],
                'faces': [0, 2, 1],   # Top -> Left -> Right -> Top
                'face_positions': {
                    0: [5, 6],  # Top face positions
                    1: [5, 6],  # Right face positions
                    2: [5, 6]   # Left face positions
                }
            }
        }

        if face_num in edge_cycles:
            cycle_info = edge_cycles[face_num]
            faces = cycle_info['faces']
            face_positions = cycle_info['face_positions']

            # Determine cycling direction
            if not is_prime:  # Clockwise
                face_order = faces
            else:  # Counterclockwise
                face_order = faces[::-1]

            # Cycle stickers between faces
            for i in range(len(face_order)):
                curr_face = face_order[i]
                next_face = face_order[(i + 1) % len(face_order)]

                if curr_face in face_positions and next_face in face_positions:
                    curr_positions = face_positions[curr_face]
                    next_positions = face_positions[next_face]

                    for curr_pos, next_pos in zip(curr_positions, next_positions):
                        old_idx = curr_face * self.num_stickers_per_face + curr_pos
                        new_idx = next_face * self.num_stickers_per_face + next_pos

                        self.rotate_idxs_old[move] = np.append(self.rotate_idxs_old[move], old_idx)
                        self.rotate_idxs_new[move] = np.append(self.rotate_idxs_new[move], new_idx)
