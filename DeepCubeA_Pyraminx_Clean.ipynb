{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# DeepCubeA: Training and Solving Pyraminx\n", "\n", "This notebook trains a deep reinforcement learning model to solve the Pyraminx puzzle using the DeepCubeA algorithm.\n", "\n", "**Paper**: [Solving the Rubik's cube with deep reinforcement learning and search](https://www.nature.com/articles/s42256-019-0070-z)\n", "\n", "**Authors**: <PERSON>, <PERSON>, <PERSON>, <PERSON>"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 1. Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive"}, "outputs": [], "source": ["# Mount Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Navigate to your DeepCubeA directory\n", "import os\n", "import sys\n", "\n", "# Update this path to match your Google Drive structure\n", "DEEPCUBE_PATH = '/content/drive/MyDrive/DeepCubeA/DeepCubeA'\n", "\n", "# Add to Python path and change directory\n", "if DEEPCUBE_PATH not in sys.path:\n", "    sys.path.append(DEEPCUBE_PATH)\n", "os.chdir(DEEPCUBE_PATH)\n", "\n", "print(f\"Current directory: {os.getcwd()}\")\n", "print(f\"DeepCubeA path added to Python path: {DEEPCUBE_PATH}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_requirements"}, "outputs": [], "source": ["# Install required packages\n", "!pip install torch torchvision torchaudio\n", "!pip install numpy matplotlib pickle-mixin\n", "\n", "# Test imports\n", "try:\n", "    from environments.pyraminx import Pyraminx\n", "    from utils.env_utils import get_environment\n", "    import torch\n", "    print(\"✅ All imports successful!\")\n", "    print(f\"PyTorch version: {torch.__version__}\")\n", "    print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")"]}, {"cell_type": "markdown", "metadata": {"id": "environment_test"}, "source": ["## 2. Test Pyraminx Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_pyraminx"}, "outputs": [], "source": ["# Test the Pyraminx environment\n", "from environments.pyraminx import Pyraminx\n", "import numpy as np\n", "\n", "# Create Pyraminx environment\n", "env = Pyraminx()\n", "\n", "print(\"=== Pyraminx Environment Info ===\")\n", "print(f\"Total stickers: {env.total_stickers}\")\n", "print(f\"Available moves: {env.moves}\")\n", "print(f\"Number of moves: {env.get_num_moves()}\")\n", "print(f\"State dimension: {env.total_stickers}\")\n", "\n", "print(\"\\n📖 Pyraminx Move Notation:\")\n", "print(\"  Tip moves (uppercase): U, R, L, B (and U', R', L', B' for counterclockwise)\")\n", "print(\"  Edge moves (lowercase): u, r, l, b (and u', r', l', b' for counterclockwise)\")\n", "print(\"  U/u = Up, R/r = Right, L/l = Left, B/b = Back\")\n", "print(\"  ' = counterclockwise rotation\")\n", "\n", "# Test scrambling\n", "print(\"\\n=== Testing Scrambling ===\")\n", "solved_states = env.generate_goal_states(1)\n", "solved_state = solved_states[0]\n", "print(f\"Solved state: {solved_state}\")\n", "print(f\"Is solved: {env.is_solved([solved_state])[0]}\")\n", "\n", "# Generate a scrambled state\n", "scrambled_states, scramble_nums = env.generate_states(1, (5, 10))\n", "scrambled_state = scrambled_states[0]\n", "scramble_depth = scramble_nums[0]\n", "\n", "print(f\"\\nScrambled state ({scramble_depth} moves): {scrambled_state}\")\n", "print(f\"Is scrambled state solved: {env.is_solved([scrambled_state])[0]}\")\n", "\n", "print(\"\\n✅ Pyraminx environment working correctly!\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## 3. Training Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_directories"}, "outputs": [], "source": ["# Create necessary directories\n", "import os\n", "\n", "directories = [\n", "    'saved_models/pyraminx/current',\n", "    'saved_models/pyraminx/target',\n", "    'results/pyraminx',\n", "    'data/pyraminx/test'\n", "]\n", "\n", "for directory in directories:\n", "    os.makedirs(directory, exist_ok=True)\n", "    print(f\"✅ Created directory: {directory}\")\n", "\n", "print(\"\\n🎯 All directories created successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "training"}, "source": ["## 4. Training the Model\n", "\n", "This will train the cost-to-go function using Approximate Value Iteration (AVI). Training may take 30-60 minutes depending on your settings."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training_phase1"}, "outputs": [], "source": ["# Training Phase 1: Initial training\n", "print(\"🚀 Starting Pyraminx Training Phase 1...\")\n", "print(\"This may take 20-40 minutes. You can monitor progress below.\")\n", "print(\"=\" * 60)\n", "\n", "!python ctg_approx/avi.py \\\n", "    --env pyraminx \\\n", "    --states_per_update 15000000 \\\n", "    --batch_size 4000 \\\n", "    --nnet_name pyraminx \\\n", "    --max_itrs 300000 \\\n", "    --loss_thresh 0.1 \\\n", "    --back_max 25 \\\n", "    --num_update_procs 10\n", "\n", "print(\"\\n✅ Training Phase 1 completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "update_target"}, "outputs": [], "source": ["# Update target network\n", "print(\"🔄 Updating target network...\")\n", "\n", "import shutil\n", "import os\n", "\n", "source_dir = 'saved_models/pyraminx/current/'\n", "target_dir = 'saved_models/pyraminx/target/'\n", "\n", "# Copy all files from current to target\n", "for filename in os.listdir(source_dir):\n", "    if os.path.isfile(os.path.join(source_dir, filename)):\n", "        shutil.copy2(os.path.join(source_dir, filename), os.path.join(target_dir, filename))\n", "        print(f\"✅ Copied {filename}\")\n", "\n", "print(\"\\n🎯 Target network updated successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training_phase2"}, "outputs": [], "source": ["# Training Phase 2: Extended training\n", "print(\"🚀 Starting Pyraminx Training Phase 2...\")\n", "print(\"This phase will further refine the model. May take 20-40 minutes.\")\n", "print(\"=\" * 60)\n", "\n", "!python ctg_approx/avi.py \\\n", "    --env pyraminx \\\n", "    --states_per_update 15000000 \\\n", "    --batch_size 4000 \\\n", "    --nnet_name pyraminx \\\n", "    --max_itrs 500000 \\\n", "    --loss_thresh 0.1 \\\n", "    --back_max 25 \\\n", "    --num_update_procs 10\n", "\n", "print(\"\\n🎉 Training Phase 2 completed!\")\n", "print(\"\\n🏆 TRAINING FINISHED! Your Pyraminx solver is ready!\")"]}, {"cell_type": "markdown", "metadata": {"id": "test_data"}, "source": ["## 5. Generate Test Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate_test_data"}, "outputs": [], "source": ["# Generate test data\n", "import pickle\n", "import numpy as np\n", "from environments.pyraminx import Pyraminx\n", "\n", "print(\"📊 Generating test data...\")\n", "\n", "env = Pyraminx()\n", "\n", "# Generate easy test cases (1-15 moves)\n", "states_easy, scramble_nums_easy = env.generate_states(500, (1, 15))\n", "data_easy = {\n", "    'states': states_easy,\n", "    'scramble_nums': scramble_nums_easy\n", "}\n", "\n", "with open('data/pyraminx/test/data_easy.pkl', 'wb') as f:\n", "    pickle.dump(data_easy, f)\n", "\n", "print(f\"✅ Generated easy test data: 500 states with scrambles 1-15\")\n", "\n", "# Generate medium test cases (10-25 moves)\n", "states_medium, scramble_nums_medium = env.generate_states(300, (10, 25))\n", "data_medium = {\n", "    'states': states_medium,\n", "    'scramble_nums': scramble_nums_medium\n", "}\n", "\n", "with open('data/pyraminx/test/data_medium.pkl', 'wb') as f:\n", "    pickle.dump(data_medium, f)\n", "\n", "print(f\"✅ Generated medium test data: 300 states with scrambles 10-25\")\n", "\n", "print(f\"\\n🎯 Test data generation completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_testing"}, "source": ["## 6. Test the Trained Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_model_loading"}, "outputs": [], "source": ["# Test model loading and heuristic function\n", "from utils import nnet_utils\n", "from utils.env_utils import get_environment\n", "import torch\n", "\n", "print(\"🧪 Testing trained model...\")\n", "\n", "try:\n", "    # Load environment\n", "    env = get_environment('pyraminx')\n", "    print(\"✅ Environment loaded\")\n", "    \n", "    # Get device\n", "    device, devices, on_gpu = nnet_utils.get_device()\n", "    print(f\"✅ Device: {device}, GPU: {on_gpu}\")\n", "    \n", "    # Load the trained model\n", "    model_dir = 'saved_models/pyraminx/current/'\n", "    heuristic_fn = nnet_utils.load_heuristic_fn(\n", "        model_dir, device, on_gpu, env.get_nnet_model(),\n", "        env, clip_zero=True, batch_size=10\n", "    )\n", "    print(\"✅ Model loaded successfully\")\n", "    \n", "    # Test with solved state\n", "    solved_states = env.generate_goal_states(1)\n", "    solved_state = solved_states[0]\n", "    states_nnet = env.state_to_nnet_input([solved_state])\n", "    heuristic_solved = heuristic_fn(states_nnet, is_nnet_format=True)\n", "    print(f\"✅ Heuristic for solved state: {heuristic_solved[0]:.3f} (should be close to 0)\")\n", "    \n", "    # Test with scrambled states\n", "    scrambled_states, scramble_nums = env.generate_states(5, (5, 15))\n", "    states_nnet = env.state_to_nnet_input(scrambled_states)\n", "    heuristics = heuristic_fn(states_nnet, is_nnet_format=True)\n", "    \n", "    print(\"\\n📊 Heuristic estimates for scrambled states:\")\n", "    for i, (scramble_depth, heuristic) in enumerate(zip(scramble_nums, heuristics)):\n", "        print(f\"  State {i+1}: {scramble_depth} moves scramble → {heuristic:.2f} estimated moves\")\n", "    \n", "    print(\"\\n🎉 Model testing successful!\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error testing model: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {"id": "solving"}, "source": ["## 7. <PERSON><PERSON>min<PERSON> Puzzles with A* Search"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "solve_easy"}, "outputs": [], "source": ["# Solve easy test cases\n", "print(\"🔍 Solving easy Pyraminx puzzles...\")\n", "print(\"This should take 2-5 minutes.\")\n", "print(\"=\" * 50)\n", "\n", "!python search_methods/astar.py \\\n", "    --states data/pyraminx/test/data_easy.pkl \\\n", "    --model_dir saved_models/pyraminx/current/ \\\n", "    --env pyraminx \\\n", "    --weight 0.7 \\\n", "    --batch_size 100 \\\n", "    --results_dir results/pyraminx_easy/ \\\n", "    --nnet_batch_size 100 \\\n", "    --language python \\\n", "    --verbose\n", "\n", "print(\"\\n✅ Easy puzzles solved!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "analyze_results"}, "outputs": [], "source": ["# Analyze puzzle results\n", "import pickle\n", "import numpy as np\n", "\n", "print(\"📊 Analyzing puzzle results...\")\n", "\n", "try:\n", "    # Load results\n", "    with open('results/pyraminx_easy/results.pkl', 'rb') as f:\n", "        results = pickle.load(f)\n", "    \n", "    # Load original test data for comparison\n", "    with open('data/pyraminx/test/data_easy.pkl', 'rb') as f:\n", "        test_data = pickle.load(f)\n", "    \n", "    print(\"=== Easy Puzzles Results ===\")\n", "    print(f\"Number of states tested: {len(results['states'])}\")\n", "    \n", "    # Calculate success rate\n", "    successful_solves = [s for s in results['solutions'] if s is not None]\n", "    success_rate = len(successful_solves) / len(results['solutions']) * 100\n", "    print(f\"Success rate: {success_rate:.1f}%\")\n", "    \n", "    if successful_solves:\n", "        solution_lengths = [len(s) for s in successful_solves]\n", "        scramble_depths = test_data['scramble_nums'][:len(successful_solves)]\n", "        \n", "        print(f\"Average solution length: {np.mean(solution_lengths):.1f} moves\")\n", "        print(f\"Solution length range: {min(solution_lengths)} - {max(solution_lengths)} moves\")\n", "        print(f\"Average scramble depth: {np.mean(scramble_depths):.1f} moves\")\n", "        \n", "        # Calculate optimality ratio\n", "        optimality_ratios = [sol_len / scr_dep for sol_len, scr_dep in zip(solution_lengths, scramble_depths) if scr_dep > 0]\n", "        if optimality_ratios:\n", "            print(f\"Average optimality ratio: {np.mean(optimality_ratios):.2f} (1.0 = optimal)\")\n", "    \n", "    # Show solve times\n", "    if 'times' in results:\n", "        solve_times = [t for t in results['times'] if t is not None]\n", "        if solve_times:\n", "            print(f\"Average solve time: {np.mean(solve_times):.2f} seconds\")\n", "    \n", "    print(\"\\n=== Sample Solutions (with standard notation) ===\")\n", "    from utils.env_utils import get_environment\n", "    env_temp = get_environment('pyraminx')\n", "    \n", "    for i in range(min(5, len(results['solutions']))):\n", "        sol = results['solutions'][i]\n", "        scramble_depth = test_data['scramble_nums'][i]\n", "        if sol is not None:\n", "            # Convert solution to standard notation\n", "            sol_notation = [env_temp.moves[move_idx] for move_idx in sol]\n", "            sol_string = ' '.join(sol_notation)\n", "            print(f\"Puzzle {i+1}: {scramble_depth} moves scramble → {len(sol)} moves solution\")\n", "            print(f\"           Solution: {sol_string}\")\n", "        else:\n", "            print(f\"Puzzle {i+1}: {scramble_depth} moves scramble → No solution found\")\n", "            \n", "except FileNotFoundError:\n", "    print(\"❌ Results file not found. The A* search may not have completed successfully.\")\n", "except Exception as e:\n", "    print(f\"❌ Error analyzing results: {e}\")"]}, {"cell_type": "markdown", "metadata": {"id": "interactive_solving"}, "source": ["## 8. Interactive Pyraminx Solving"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "interactive_solve"}, "outputs": [], "source": ["# Interactive solving - create and solve a custom scramble\n", "from environments.pyraminx import Pyraminx\n", "from utils import nnet_utils\n", "from utils.env_utils import get_environment\n", "from search_methods.astar import AStar\n", "import torch\n", "import time\n", "\n", "print(\"🎮 Interactive Pyraminx Solving\")\n", "print(\"=\" * 40)\n", "\n", "# Load environment and model\n", "env = get_environment('pyraminx')\n", "device, devices, on_gpu = nnet_utils.get_device()\n", "heuristic_fn = nnet_utils.load_heuristic_fn(\n", "    'saved_models/pyraminx/current/', device, on_gpu, env.get_nnet_model(),\n", "    env, clip_zero=True, batch_size=1\n", ")\n", "\n", "# Create a custom scramble\n", "scramble_depth = 15  # You can change this\n", "print(f\"🎲 Creating a {scramble_depth}-move scramble...\")\n", "\n", "scrambled_states, actual_depths = env.generate_states(1, (scramble_depth, scramble_depth))\n", "state = scrambled_states[0]\n", "actual_depth = actual_depths[0]\n", "\n", "print(f\"✅ Generated scramble with {actual_depth} moves\")\n", "print(f\"State representation: {state}\")\n", "\n", "# Get heuristic estimate\n", "states_nnet = env.state_to_nnet_input([state])\n", "heuristic_value = heuristic_fn(states_nnet, is_nnet_format=True)\n", "print(f\"🧠 Heuristic estimate: {heuristic_value[0]:.2f} moves to solve\")\n", "print(f\"📏 Actual scramble depth: {actual_depth} moves\")\n", "\n", "# Solve with A*\n", "print(f\"\\n🔍 Solving with A* search...\")\n", "start_time = time.time()\n", "\n", "astar = AStar([state], env, heuristic_fn, [0.7])\n", "\n", "max_steps = 1000\n", "step = 0\n", "while not astar.has_found_goal()[0] and step < max_steps:\n", "    astar.step(heuristic_fn, batch_size=1, verbose=False)\n", "    step += 1\n", "    \n", "    if step % 100 == 0:\n", "        print(f\"  Step {step}...\")\n", "\n", "solve_time = time.time() - start_time\n", "\n", "if astar.has_found_goal()[0]:\n", "    goal_node = astar.get_goal_node_smallest_path_cost(0)\n", "    solution = goal_node.get_path()\n", "    \n", "    print(f\"\\n🎉 SOLVED!\")\n", "    print(f\"⏱️  Solve time: {solve_time:.2f} seconds\")\n", "    print(f\"🎯 Solution length: {len(solution)} moves\")\n", "    print(f\"📊 Search steps: {step}\")\n", "    print(f\"🏆 Optimality ratio: {len(solution) / actual_depth:.2f}\")\n", "    \n", "    # Convert move indices to standard notation\n", "    solution_notation = [env.moves[move_idx] for move_idx in solution]\n", "    print(f\"\\n🔧 Solution moves (indices): {solution}\")\n", "    print(f\"🎯 Solution moves (notation): {solution_notation}\")\n", "    print(f\"📝 Solution string: {' '.join(solution_notation)}\")\n", "    \n", "    # Verify solution\n", "    current_state = state\n", "    for move in solution:\n", "        current_state = env.next_state([current_state], [move])[0]\n", "    \n", "    is_solved = env.is_solved([current_state])[0]\n", "    print(f\"\\n✅ Solution verification: {'PASSED' if is_solved else 'FAILED'}\")\n", "    \n", "else:\n", "    print(f\"\\n❌ Could not solve within {max_steps} steps\")\n", "    print(f\"⏱️  Search time: {solve_time:.2f} seconds\")"]}, {"cell_type": "markdown", "metadata": {"id": "summary"}, "source": ["## 9. <PERSON><PERSON><PERSON> and Next Steps\n", "\n", "🎉 **Congratulations!** You have successfully:\n", "\n", "1. ✅ Trained a DeepCubeA model to solve the Pyraminx\n", "2. ✅ Generated test data with various difficulty levels\n", "3. ✅ Tested the model's solving capabilities\n", "4. ✅ Solved puzzles with solutions in standard notation\n", "\n", "### Model Performance Expectations:\n", "- **Easy puzzles (1-15 moves)**: Should achieve >95% success rate\n", "- **Medium puzzles (10-25 moves)**: Should achieve >85% success rate  \n", "\n", "### Pyraminx Move Notation Used:\n", "- **Tip moves**: U, R, L, B (and U', R', L', B' for counterclockwise)\n", "- **Edge moves**: u, r, l, b (and u', r', l', b' for counterclockwise)\n", "\n", "### Next Steps:\n", "1. **Experiment with different weights** in A* search (try 0.5, 0.8, 0.9)\n", "2. **Test on harder scrambles** (25+ moves)\n", "3. **Compare with other solving methods**\n", "4. **Adapt the code for other puzzles** (<PERSON><PERSON><PERSON>'s cube, 15-puzzle, etc.)\n", "\n", "**Paper Reference**: [Solving the Rubik's cube with deep reinforcement learning and search](https://www.nature.com/articles/s42256-019-0070-z)"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}