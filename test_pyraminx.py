#!/usr/bin/env python3
"""
Test script for Pyraminx implementation in DeepCubeA
This script verifies that the Pyraminx environment works correctly.
"""

import numpy as np
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from environments.pyraminx import Pyraminx, PyraminxState
from utils.env_utils import get_environment


def test_basic_functionality():
    """Test basic Pyraminx functionality"""
    print("=== Testing Basic Pyraminx Functionality ===")
    
    # Create environment
    env = Pyraminx()
    print(f"✓ Pyraminx environment created")
    print(f"  - Number of moves: {env.get_num_moves()}")
    print(f"  - Total stickers: {env.total_stickers}")
    print(f"  - Tip moves: {[m for m in env.moves if m[0].isupper()]}")
    print(f"  - Edge moves: {[m for m in env.moves if m[0].islower()]}")
    
    # Test goal state generation
    goal_states = env.generate_goal_states(5)
    print(f"✓ Generated {len(goal_states)} goal states")
    
    # Test if goal states are actually solved
    is_solved = env.is_solved(goal_states)
    print(f"✓ Goal states solved check: {np.all(is_solved)}")
    
    # Test state representation
    nnet_input = env.state_to_nnet_input(goal_states[:1])
    print(f"✓ Neural network input shape: {nnet_input[0].shape}")
    print(f"  - Input range: [{nnet_input[0].min():.3f}, {nnet_input[0].max():.3f}]")
    
    return env


def test_moves_and_transitions():
    """Test move application and state transitions"""
    print("\n=== Testing Moves and State Transitions ===")
    
    env = Pyraminx()
    
    # Start with solved state
    solved_state = env.generate_goal_states(1)[0]
    print(f"✓ Starting with solved state")
    
    # Apply each move and check reversibility
    for move_idx, move_name in enumerate(env.moves):
        # Apply move
        next_states, costs = env.next_state([solved_state], move_idx)
        next_state = next_states[0]
        
        # Check transition cost
        assert costs[0] == 1.0, f"Unexpected transition cost: {costs[0]}"
        
        # Apply reverse move
        prev_states = env.prev_state([next_state], move_idx)
        prev_state = prev_states[0]
        
        # Check if we get back to original state
        if solved_state == prev_state:
            print(f"✓ Move {move_name}: Reversible")
        else:
            print(f"✗ Move {move_name}: NOT reversible!")
            return False
    
    print("✓ All moves are reversible")
    return True


def test_scrambling_and_generation():
    """Test state scrambling and generation"""
    print("\n=== Testing State Scrambling and Generation ===")
    
    env = Pyraminx()
    
    # Generate scrambled states
    scrambled_states, scramble_nums = env.generate_states(10, (1, 10))
    print(f"✓ Generated {len(scrambled_states)} scrambled states")
    print(f"  - Scramble depths: {scramble_nums}")
    
    # Check that scrambled states are not solved (except possibly 0-move scrambles)
    is_solved = env.is_solved(scrambled_states)
    non_trivial_scrambles = [i for i, depth in enumerate(scramble_nums) if depth > 0]
    
    if non_trivial_scrambles:
        non_trivial_solved = [is_solved[i] for i in non_trivial_scrambles]
        if not any(non_trivial_solved):
            print("✓ Non-trivial scrambles are not solved")
        else:
            print(f"⚠ Some non-trivial scrambles appear solved: {non_trivial_solved}")
    
    # Test state expansion
    test_states = scrambled_states[:3]
    expanded_states, transition_costs = env.expand(test_states)
    
    print(f"✓ Expanded {len(test_states)} states")
    print(f"  - Each state expanded to {len(expanded_states[0])} children")
    print(f"  - Transition costs shape: {len(transition_costs)}, {len(transition_costs[0])}")
    
    return True


def test_neural_network_model():
    """Test neural network model creation"""
    print("\n=== Testing Neural Network Model ===")
    
    env = Pyraminx()
    
    try:
        model = env.get_nnet_model()
        print(f"✓ Neural network model created: {type(model).__name__}")
        
        # Test forward pass with dummy data
        import torch
        dummy_input = torch.randint(0, 4, (5, env.total_stickers))
        
        with torch.no_grad():
            output = model(dummy_input)
        
        print(f"✓ Forward pass successful")
        print(f"  - Input shape: {dummy_input.shape}")
        print(f"  - Output shape: {output.shape}")
        print(f"  - Output range: [{output.min().item():.3f}, {output.max().item():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"✗ Neural network test failed: {e}")
        return False


def test_environment_utils():
    """Test environment utilities integration"""
    print("\n=== Testing Environment Utils Integration ===")
    
    try:
        env = get_environment('pyraminx')
        print(f"✓ Environment loaded via utils: {type(env).__name__}")
        
        # Quick functionality test
        states = env.generate_goal_states(2)
        is_solved = env.is_solved(states)
        
        if np.all(is_solved):
            print("✓ Environment utils integration working")
            return True
        else:
            print("✗ Environment utils integration failed")
            return False
            
    except Exception as e:
        print(f"✗ Environment utils test failed: {e}")
        return False


def test_state_hashing():
    """Test state hashing and equality"""
    print("\n=== Testing State Hashing and Equality ===")
    
    env = Pyraminx()
    
    # Create identical states
    state1 = env.generate_goal_states(1)[0]
    state2 = env.generate_goal_states(1)[0]
    
    # Test equality
    if state1 == state2:
        print("✓ Identical states are equal")
    else:
        print("✗ Identical states are not equal")
        return False
    
    # Test hashing
    if hash(state1) == hash(state2):
        print("✓ Identical states have same hash")
    else:
        print("✗ Identical states have different hashes")
        return False
    
    # Test different states
    scrambled_states, _ = env.generate_states(2, (5, 5))
    state3, state4 = scrambled_states[0], scrambled_states[1]
    
    if state3 != state4:
        print("✓ Different states are not equal")
    else:
        print("⚠ Different scrambled states happen to be equal (rare but possible)")
    
    # Test set operations (requires proper hashing)
    state_set = {state1, state2, state3, state4}
    print(f"✓ State set operations work, unique states: {len(state_set)}")
    
    return True


def run_all_tests():
    """Run all tests"""
    print("Starting Pyraminx Implementation Tests...\n")
    
    tests = [
        test_basic_functionality,
        test_moves_and_transitions,
        test_scrambling_and_generation,
        test_neural_network_model,
        test_environment_utils,
        test_state_hashing
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test.__name__}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pyraminx implementation is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
